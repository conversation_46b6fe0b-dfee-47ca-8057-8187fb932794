实现一个高性能的 ai 服务聚合平台，需要综合考虑多个方面。以下是一些关键的步骤和考虑因素：

- **服务集成**：支持多种 ai 供应商的API集成，如OpenAI、Google AI等。
- **密钥池管理**：管理多个API密钥池，确保高可用性和安全性。支持不同的密钥策略，如轮询、权重分配等。
- **负载均衡**：实现请求的负载均衡，以优化资源利用和提高响应速度。
- **请求路由**：根据请求类型和参数，将请求路由到最合适的AI服务。
- **错误处理和重试**：处理API调用中的错误，并实现重试机制以提高成功率。
- **监控和日志**：实时监控服务状态和性能，记录详细的日志以便于调试和优化。
- **缓存机制**：实现请求结果的缓存，减少重复计算和API调用次数。
- **异步机制**：支持异步处理，提高系统吞吐量。

实现顶层设计, 暴露主要的功能, 再根据具体的供应商的 sdk 进行实现

- **文本生成**：处理文本生成任务。
  - 支持通用参数和各个供应商特有的参数。
  - 封装供应商的 sdk，提供统一的接口。
- **图像生成**：处理图像生成任务。
- **流式输出接口**：对于需要长时间运行或生成大量数据的任务，如文本生成、图像生成等，提供流式输出接口以减少内存使用和提高响应速度。

请基于当前代码库实现一个高性能的AI服务聚合平台。请按照以下具体要求进行开发：

## 核心架构设计
1. **创建统一的服务接口层**：设计抽象基类和接口，定义标准的AI服务调用规范
2. **实现供应商适配器模式**：为每个AI供应商（OpenAI、Google AI、Anthropic等）创建独立的适配器类
3. **建立配置管理系统**：支持动态配置加载，包括API密钥、服务端点、超时设置等

## 具体功能模块实现

### 1. 密钥池管理模块
- 实现密钥轮询、加权分配、故障转移策略
- 支持密钥健康检查和自动禁用/启用
- 提供密钥使用统计和监控接口

### 2. 请求路由和负载均衡
- 根据模型类型、请求参数、供应商可用性进行智能路由
- 实现基于响应时间和成功率的动态负载均衡
- 支持A/B测试和灰度发布机制

### 3. 错误处理和重试机制
- 实现指数退避重试策略
- 区分不同类型的错误（网络错误、API限制、认证失败等）
- 提供熔断器模式防止级联失败

### 4. 缓存系统
- 实现基于请求参数的智能缓存键生成
- 支持多级缓存（内存缓存、Redis缓存）
- 提供缓存失效策略和手动清理接口

### 5. 监控和日志系统
- 记录详细的请求/响应日志，包括延迟、成功率、错误类型
- 实现性能指标收集（QPS、平均响应时间、错误率）
- 提供健康检查端点和监控仪表板

## 业务功能接口

### 文本生成服务
- 统一的文本生成接口，支持同步和异步调用
- 参数标准化：将不同供应商的参数映射到统一格式
- 流式输出支持：实现Server-Sent Events或WebSocket流式响应
- 支持的功能：对话生成、文本补全、代码生成、翻译等

### 图像生成服务
- 统一的图像生成接口
- 支持不同的图像尺寸、风格、质量参数
- 实现图像结果的存储和CDN分发
- 支持批量生成和进度查询

## 技术要求
1. **使用适当的编程语言和框架**（根据当前代码库技术栈）
2. **实现完整的单元测试和集成测试**
3. **提供详细的中文注释和API文档**
4. **遵循SOLID原则和设计模式最佳实践**
5. **确保代码的可扩展性和可维护性**

## 交付物
1. 完整的代码实现，包括所有模块和接口
2. 配置文件模板和部署说明
3. API文档和使用示例
4. 单元测试和集成测试代码
5. 性能测试报告和优化建议

请先制定详细的实施计划，并逐步实现各个模块。