"""
Google AI 供应商适配器

实现Google AI (Gemini) API的完整适配，包括：
- 文本生成（Gemini Pro）
- 流式输出支持
- 错误处理和重试
"""

import json
import time
from typing import Any, AsyncIterator, Dict, List, Union
from uuid import uuid4

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    MessageRole,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
)
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.utils.key_manager import KeyManager


class GoogleAIProvider(BaseProvider):
    """Google AI供应商适配器"""
    
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        """初始化Google AI适配器
        
        Args:
            config: Google AI配置
            key_manager: 密钥管理器
        """
        super().__init__("google_ai", config, key_manager)
        
        # 设置基础URL
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        
        # 支持的模型类型
        self._supported_model_types = [ModelType.TEXT_GENERATION]
        
        # 支持的模型列表
        self._supported_models = [
            "gemini-pro",
            "gemini-pro-vision",
            "gemini-1.0-pro",
            "gemini-1.0-pro-001",
            "gemini-1.0-pro-latest",
            "gemini-1.0-pro-vision-latest",
        ]
        
        # 模型映射
        self._model_mapping = {
            "gemini-latest": "gemini-pro",
            "gemini": "gemini-pro",
        }
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """执行健康检查"""
        try:
            # 发送一个简单的模型列表请求
            url = f"{self.base_url}/models?key={api_key}"
            response = await self._make_request("GET", url)
            return response.status_code == 200
        except Exception:
            return False
    
    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """实现文本生成"""
        # 构建请求数据
        request_data = self._build_text_request(request)
        
        # 构建URL
        model_name = self.map_model_name(request.model)
        if request.stream:
            url = f"{self.base_url}/models/{model_name}:streamGenerateContent?key={api_key}"
            return self._handle_stream_response(request, url, request_data)
        else:
            url = f"{self.base_url}/models/{model_name}:generateContent?key={api_key}"
            response = await self._make_request("POST", url, json_data=request_data)
            response_data = response.json()
            return self._parse_text_response(response_data, request)
    
    def _build_text_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建文本生成请求数据"""
        # 转换消息格式为Google AI格式
        contents = []
        
        # 合并连续的相同角色消息
        current_role = None
        current_parts = []
        
        for msg in request.messages:
            # 映射角色
            if msg.role == MessageRole.SYSTEM:
                # Google AI没有system角色，将其作为user消息处理
                role = "user"
            elif msg.role == MessageRole.USER:
                role = "user"
            elif msg.role == MessageRole.ASSISTANT:
                role = "model"
            else:
                # 跳过不支持的角色
                continue
            
            if role != current_role:
                # 保存之前的内容
                if current_role and current_parts:
                    contents.append({
                        "role": current_role,
                        "parts": current_parts
                    })
                
                # 开始新的角色
                current_role = role
                current_parts = []
            
            # 添加消息内容
            current_parts.append({"text": msg.content})
        
        # 添加最后的内容
        if current_role and current_parts:
            contents.append({
                "role": current_role,
                "parts": current_parts
            })
        
        # 构建请求数据
        request_data = {"contents": contents}
        
        # 添加生成配置
        generation_config = {}
        
        if request.max_tokens is not None:
            generation_config["maxOutputTokens"] = request.max_tokens
        if request.temperature is not None:
            generation_config["temperature"] = request.temperature
        if request.top_p is not None:
            generation_config["topP"] = request.top_p
        if request.top_k is not None:
            generation_config["topK"] = request.top_k
        if request.stop is not None:
            if isinstance(request.stop, str):
                generation_config["stopSequences"] = [request.stop]
            else:
                generation_config["stopSequences"] = request.stop
        
        if generation_config:
            request_data["generationConfig"] = generation_config
        
        # 安全设置（可选）
        safety_settings = [
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            }
        ]
        request_data["safetySettings"] = safety_settings
        
        return request_data
    
    def _parse_text_response(
        self,
        response_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> TextGenerationResponse:
        """解析文本生成响应"""
        choices = []
        
        # 解析候选响应
        candidates = response_data.get("candidates", [])
        for i, candidate in enumerate(candidates):
            content = candidate.get("content", {})
            parts = content.get("parts", [])
            
            # 合并所有文本部分
            text_content = ""
            for part in parts:
                if "text" in part:
                    text_content += part["text"]
            
            message = Message(
                role=MessageRole.ASSISTANT,
                content=text_content
            )
            
            # 获取结束原因
            finish_reason = candidate.get("finishReason", "stop")
            if finish_reason == "STOP":
                finish_reason = "stop"
            elif finish_reason == "MAX_TOKENS":
                finish_reason = "length"
            elif finish_reason == "SAFETY":
                finish_reason = "content_filter"
            
            choice = TextGenerationChoice(
                index=i,
                message=message,
                finish_reason=finish_reason
            )
            choices.append(choice)
        
        # 解析使用量（Google AI可能不提供详细的token计数）
        usage_metadata = response_data.get("usageMetadata", {})
        usage = Usage(
            prompt_tokens=usage_metadata.get("promptTokenCount", 0),
            completion_tokens=usage_metadata.get("candidatesTokenCount", 0),
            total_tokens=usage_metadata.get("totalTokenCount", 0)
        )
        
        return TextGenerationResponse(
            id=str(uuid4()),
            object="chat.completion",
            created=int(time.time()),
            model=request.model,
            choices=choices,
            usage=usage,
            provider=self.name,
            request_id=str(uuid4()),
            processing_time=0.0
        )
    
    async def _handle_stream_response(
        self,
        request: TextGenerationRequest,
        url: str,
        request_data: Dict[str, Any]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理流式响应"""
        async for chunk in self._make_request(
            "POST",
            url,
            json_data=request_data,
            stream=True
        ):
            # 解析流式数据
            chunk_str = chunk.decode('utf-8')
            for line in chunk_str.split('\n'):
                line = line.strip()
                if line.startswith('data: '):
                    data_str = line[6:]
                    
                    try:
                        data = json.loads(data_str)
                        
                        # 解析候选响应
                        candidates = data.get("candidates", [])
                        choices = []
                        
                        for i, candidate in enumerate(candidates):
                            content = candidate.get("content", {})
                            parts = content.get("parts", [])
                            
                            # 获取增量文本
                            delta_text = ""
                            for part in parts:
                                if "text" in part:
                                    delta_text += part["text"]
                            
                            choice_data = {
                                "index": i,
                                "delta": {
                                    "role": "assistant",
                                    "content": delta_text
                                }
                            }
                            
                            # 检查是否结束
                            finish_reason = candidate.get("finishReason")
                            if finish_reason:
                                if finish_reason == "STOP":
                                    choice_data["finish_reason"] = "stop"
                                elif finish_reason == "MAX_TOKENS":
                                    choice_data["finish_reason"] = "length"
                                elif finish_reason == "SAFETY":
                                    choice_data["finish_reason"] = "content_filter"
                            
                            choices.append(choice_data)
                        
                        # 构建流式响应块
                        chunk_response = TextGenerationStreamChunk(
                            id=str(uuid4()),
                            object="chat.completion.chunk",
                            created=int(time.time()),
                            model=request.model,
                            choices=choices,
                            provider=self.name,
                            request_id=str(uuid4())
                        )
                        
                        yield chunk_response
                        
                    except json.JSONDecodeError:
                        continue
    
    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """Google AI暂不支持图像生成"""
        raise NotImplementedError("Google AI暂不支持图像生成功能")
    
    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认的HTTP头"""
        return {
            "User-Agent": f"AI-Gen-Hub/1.0.0 (google_ai)",
            "Content-Type": "application/json",
        }
